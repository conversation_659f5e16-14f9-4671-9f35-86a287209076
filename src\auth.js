import NextAuth from "next-auth"
import <PERSON> from "next-auth/providers/google"
import Nodemailer from "next-auth/providers/nodemailer"
import { MongoDBAdapter } from "@auth/mongodb-adapter"
import clientPromise from "@/libs/mongodb"

export const { handlers, signIn, signOut, auth } = NextAuth({
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    Google({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
    }),
    Nodemailer({
      server: JSON.parse(process.env.EMAIL_SERVER),
      from: process.env.EMAIL_FROM,
    }),
  ],
  pages: {
    verifyRequest: "/auth/verify-request", // Custom magic link verification page
  },
  callbacks: {
    async signIn({ user }) {
      // Auto-assign admin role to specific email
      if (user.email === "<EMAIL>") {
        user.role = "admin"
      } else {
        user.role = "user"
      }
      return true
    },
    async session({ session, user }) {
      // Add user role and other fields to session
      if (user) {
        session.user.id = user.id
        session.user.role = user.role || "user"
        session.user.phone = user.phone
        session.user.projects = user.projects || []
        session.user.dateCreated = user.dateCreated
      }
      return session
    },
    async jwt({ token, user, account }) {
      // Handle account linking for same email across providers
      if (account && user) {
        token.role = user.role
        token.phone = user.phone
        token.projects = user.projects
        token.dateCreated = user.dateCreated
      }
      return token
    },
  },
  events: {
    async createUser({ user }) {
      // Set default values for new users
      const client = await clientPromise
      const db = client.db()

      await db.collection("users").updateOne(
        { _id: user.id },
        {
          $set: {
            username: user.name || user.email?.split('@')[0] || 'User',
            phone: null,
            projects: [],
            role: user.email === "<EMAIL>" ? "admin" : "user",
            dateCreated: new Date(),
          }
        }
      )
    },
    async linkAccount({ user }) {
      // Handle account linking for users with same email
      const client = await clientPromise
      const db = client.db()

      // Update user role if it's the admin email
      if (user.email === "<EMAIL>") {
        await db.collection("users").updateOne(
          { _id: user.id },
          { $set: { role: "admin" } }
        )
      }
    },
  },
  session: {
    strategy: "database",
  },
})