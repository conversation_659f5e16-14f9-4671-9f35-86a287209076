import NextAuth from "next-auth"
import Google from "next-auth/providers/google"

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    Google({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
    }),
  ],
  pages: {
    signIn: "/auth/signin",
  },
  callbacks: {
    async signIn({ user }) {
      // Auto-assign admin role to specific email
      if (user.email === "<EMAIL>") {
        user.role = "admin"
      } else {
        user.role = "user"
      }
      return true
    },
    async session({ session, token }) {
      // Add user role and other fields to session
      if (token) {
        session.user.id = token.sub
        session.user.role = token.role || "user"
        session.user.phone = token.phone
        session.user.projects = token.projects || []
        session.user.dateCreated = token.dateCreated
      }
      return session
    },
    async jwt({ token, user, account }) {
      // Handle account linking and role assignment
      if (account && user) {
        token.role = user.email === "<EMAIL>" ? "admin" : "user"
        token.phone = user.phone || null
        token.projects = user.projects || []
        token.dateCreated = user.dateCreated || new Date().toISOString()

        // Store user in database via API call
        try {
          await fetch(`${process.env.AUTH_URL}/api/auth/store-user`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              id: user.id,
              name: user.name,
              email: user.email,
              image: user.image,
              role: token.role,
              phone: token.phone,
              projects: token.projects,
              dateCreated: token.dateCreated
            })
          })
        } catch (error) {
          console.error('Failed to store user:', error)
        }
      }
      return token
    },
  },
  session: {
    strategy: "jwt",
  },
})