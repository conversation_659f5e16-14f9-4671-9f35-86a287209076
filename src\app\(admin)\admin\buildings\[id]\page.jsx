// src/app/admin/buildings/[id]/page.jsx
// View building details page

"use client";

import { useState, useEffect, use } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  HiChevronLeft,
  HiPencil,
  HiTrash,
  HiEye,
  HiOfficeBuilding,
  HiHome,
  HiCalendar,
  HiCurrencyDollar,
  HiTag,
  HiLocationMarker,
  HiDocumentText,
  HiPhotograph,
  HiCube,
  HiDocument
} from 'react-icons/hi';
import ExperienceWrapper from '@/components/experience/ExperienceWrapperDashboard';
import TextWrapper from '@/components/BuildingPage/TextWrapper';

export default function ViewBuildingPage({ params }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [building, setBuilding] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [buildingData, setBuildingData] = useState({});
  const [error, setError] = useState('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const buildingId = resolvedParams.id;

  // Check for success messages
  useEffect(() => {
    if (searchParams.get('updated') === 'true') {
      setShowSuccessMessage(true);
      setTimeout(() => setShowSuccessMessage(false), 5000);
    }
  }, [searchParams]);

  // Fetch building data - moved before conditional returns
  useEffect(() => {
    const fetchBuilding = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/buildings/${buildingId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Building not found');
          }
          throw new Error('Failed to fetch building data');
        }

        const data = await response.json();
        setBuilding(data.building);
        setError('');
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    if (buildingId) {
      fetchBuilding();
    }
  }, [buildingId]);

  // Redirect if not authenticated
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Handle delete
  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete "${building.projectTitle}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/buildings/${buildingId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete building');
      }

      router.push('/admin/buildings?deleted=true');
    } catch (err) {
      alert('Error deleting building: ' + err.message);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading building data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 border border-red-200 rounded-md p-6 text-center">
            <div className="flex justify-center">
              <svg className="h-12 w-12 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-red-800 mt-2">Error Loading Building</h3>
            <p className="text-red-700 mt-1">{error}</p>
            <div className="mt-4">
              <Link
                href="/admin/buildings"
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Back to Buildings
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!building) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white -border-b border-neutral-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin/buildings"
                className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
              >
                <HiChevronLeft className="w-5 h-5 mr-2" />
                <span className="font-light">Back to Buildings</span>
              </Link>
              <div className="h-6 w-px bg-neutral-300"></div>
              <Link href="/" className="text-2xl font-thin tracking-widest text-neutral-900 hover:text-neutral-700 transition-colors">
                luyari.
              </Link>
            </div>
            <div className="text-sm text-neutral-600 font-light">
              Building Details
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex flex-col gap-4">
          <Link className='text-sm text-gray-500 capitalize' href={'/admin/buildings'}>back</Link>
          {/* 3D Experience Section */}
          <div className="order-2 lg:order-1">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="flex px-2 bg-neutral-50 border border-neutral-200 rounded-lg overflow-hidden"
              style={{ height: '600px' }}
            >
              <div className="wrap relative gap-4 w-1/2 h-full flex flex-col items-center justify-center text-neutral-500">
                <div className="absolute w-full top-0 mx-auto h-20 p-10 border-b border-neutral-200">
                  <h2 className="chaumet-heading text-xl text-neutral-900 flex items-center">
                    <HiCube className="w-5 h-5 mr-2" />
                    3D Experience
                  </h2>
                </div>
                <div className="flex items-center justify-center h-full w-full">
                  {false
                    ? <ExperienceWrapper data={building} />
                    : <div className="text-center items-center justify-center">
                        {/* <HiCube className="w-16 h-16 mx-auto mb-4 text-neutral-300" /> */}
                        <p className="font-light">3D Experience Loading...</p>
                    </div>
                  }
                </div>
              </div>
              <div className='h-full relative w-1/2 p-5 text-gray-500'>
                <div className="absolute flex top-0 mx-auto left-0 right-0 h-20 p-10 border-b border-neutral-200">
                  <h2 className="chaumet-heading text-xl mb-3 text-neutral-900 flex items-center">
                    {/* <HiCube className="w-5 h-5 mr-2" /> */}
                    Building Data
                  </h2>
                </div>
                <TextWrapper data={building}/>
              </div>
            </motion.div>
          </div>

          {/* Building Details Section */}
          <div className="order-1 lg:order-2 space-y-8">
            {/* Success Message */}
            {showSuccessMessage && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4"
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-green-800 font-light">
                      Building updated successfully!
                    </p>
                  </div>
                </div>
              </motion.div>
            )}

            <div className='flex flex-col max-h-fit'>
              {/* Page Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="mb-8"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="chaumet-heading text-3xl text-neutral-900 mb-2 flex items-center">
                      <HiOfficeBuilding className="w-8 h-8 mr-3 text-neutral-600" />
                      {building.projectTitle}
                    </h1>
                    <p className="text-neutral-600 font-light text-lg">{building.buildingTitle}</p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Link
                      href={`/admin/buildings/${buildingId}/edit`}
                      className="chaumet-button border-neutral-900 text-neutral-900 group"
                    >
                      <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center">
                        <HiPencil className="w-4 h-4 mr-2" />
                        Edit Building
                      </span>
                    </Link>
                    {session?.user?.role === 'admin' && (
                      <button
                        onClick={handleDelete}
                        className="chaumet-button border-red-600 text-red-600 group hover:bg-red-600"
                      >
                        <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center">
                          <HiTrash className="w-4 h-4 mr-2" />
                          Delete
                        </span>
                      </button>
                    )}
                  </div>
                </div>
                <div className="chaumet-divider mt-6" style={{ width: '4rem' }} />
              </motion.div>

              {/* Basic Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="bg-white p-6 rounded-lg border border-neutral-200"
              >
                <h2 className="chaumet-heading text-xl text-neutral-900 mb-6 flex items-center">
                  <HiTag className="w-5 h-5 mr-2" />
                  Basic Information
                </h2>
                <div className="flex gap-6 w-full justify-between">
                  <div>
                    <label className="block text-sm font-light text-neutral-600 uppercase tracking-widest mb-2">Building Type</label>
                    <span className="inline-flex px-3 py-1 text-sm font-light rounded-full bg-neutral-100 text-neutral-700 border border-neutral-200">
                      {building.buildingType.replace('-', ' ')}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-light text-neutral-600 uppercase tracking-widest mb-2">Price</label>
                    <p className="text-lg font-light text-neutral-900 flex items-center">
                      <HiCurrencyDollar className="w-5 h-5 mr-1" />
                      {building.price}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-light text-neutral-600 uppercase tracking-widest mb-2">Building Role</label>
                    <p className="text-neutral-900 font-light">{building.buildingRole}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-light text-neutral-600 uppercase tracking-widest mb-2">Created</label>
                    <p className="text-neutral-900 font-light flex items-center">
                      <HiCalendar className="w-4 h-4 mr-2" />
                      {new Date(building.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Description */}
            {/* <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Description</h2>
              <p className="text-gray-700 leading-relaxed">{building.desc}</p>
            </div> */}

            {/* Features */}
            {/* <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Features</h2>
              <p className="text-gray-700 leading-relaxed">{building.features}</p>
            </div> */}

            {/* Building Highlights */}
            {/* {building.buildingHighlights && building.buildingHighlights.length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Building Highlights</h2>
                <div className="space-y-4">
                  {building.buildingHighlights.map((highlight, index) => (
                    <div key={index} className="border-l-4 border-blue-500 pl-4">
                      <h3 className="font-medium text-gray-900">{highlight.title}</h3>
                      <p className="text-gray-700 mt-1">{highlight.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )} */}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Building Summary */}
            {/* <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Building Summary</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Length:</span>
                  <span className="font-medium">{building.buildingSummary.length}m</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Width:</span>
                  <span className="font-medium">{building.buildingSummary.width}m</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Bedrooms:</span>
                  <span className="font-medium">{building.buildingSummary.beds}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Bathrooms:</span>
                  <span className="font-medium">{building.buildingSummary.baths}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Levels:</span>
                  <span className="font-medium">{building.buildingSummary.levels}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Car Spaces:</span>
                  <span className="font-medium">{building.buildingSummary.cars}</span>
                </div>
              </div>
            </div> */}

            {/* Tags */}
            {/* {building.tags && building.tags.length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Tags</h2>
                <div className="flex flex-wrap gap-2">
                  {building.tags.map((tag, index) => (
                    <span key={index} className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )} */}

            {/* Colors */}
            {/* {building.color && building.color.length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Colors</h2>
                <div className="flex flex-wrap gap-2">
                  {building.color.map((color, index) => (
                    <span key={index} className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                      {color}
                    </span>
                  ))}
                </div>
              </div>
            )} */}

            {/* Collections */}
            {/* {building.collections && building.collections.length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Collections</h2>
                <div className="flex flex-wrap gap-2">
                  {building.collections.map((collection, index) => (
                    <span key={index} className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                      {collection}
                    </span>
                  ))}
                </div>
              </div>
            )} */}
          </div>
        </div>

        {/* File Uploads Summary */}
        {/* <div className="mt-8 bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Uploaded Files</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              { key: 'renders', label: 'Renders' },
              { key: 'drawings', label: 'Drawings' },
              { key: '_360sImages', label: '360° Images' },
              { key: 'modelsFiles', label: 'Model Files' },
              { key: 'hideLevel', label: 'Hide Level Files' },
              { key: 'supportFiles', label: 'Support Files' },
              { key: 'roomSnaps', label: 'Room Snaps' },
              { key: 'presentationDrawings', label: 'Presentation Drawings' },
              { key: 'constructionDrawingsPdf', label: 'Construction Drawings (PDF)' },
              { key: 'constructionDrawingsDwg', label: 'Construction Drawings (DWG)' }
            ].map(({ key, label }) => (
              <div key={key} className="p-3 border border-gray-200 rounded-md">
                <h3 className="font-medium text-gray-900">{label}</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {building[key] ? building[key].length : 0} files
                </p>
              </div>
            ))}
          </div>
        </div> */}
      </main>

      {/* Footer */}
      <footer className="bg-neutral-50 border-t border-neutral-200 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-neutral-600 font-light text-sm">
              Building Details - Luyari Architecture
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
