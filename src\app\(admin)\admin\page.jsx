import PageWrapper from '@/components/PageWrapper'
import UsersManagement from '@/components/admin/UsersManagement'
import { auth } from '@/auth'
import { redirect } from 'next/navigation'
import React from 'react'

export default async function AdminPage() {
  const session = await auth()

  // Check if user is authenticated and is admin
  if (!session?.user || session.user.role !== 'admin') {
    redirect('/auth/signin')
  }

  return (
    <PageWrapper>
      <div className='flex w-full flex-col gap-6'>
        <div className='border-b border-gray-200 pb-4'>
          <h1 className='text-3xl font-bold text-gray-900'>Admin Dashboard</h1>
          <p className='text-lg text-gray-600 mt-2'>Manage users and system settings</p>
        </div>

        <div className='bg-white rounded-lg shadow-sm border'>
          <div className='px-6 py-4 border-b border-gray-200'>
            <h2 className='text-xl font-semibold text-gray-900'>Users Management</h2>
            <p className='text-sm text-gray-600 mt-1'>View, edit, and manage user accounts</p>
          </div>
          <div className='p-6'>
            <UsersManagement />
          </div>
        </div>
      </div>
    </PageWrapper>
  )
}
