// src/app/admin/buildings/create/page.jsx
// Create new building page

"use client";

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { HiChevronLeft, HiExclamationCircle, HiOfficeBuilding } from 'react-icons/hi';
import BuildingForm from '@/components/forms/BuildingForm';

export default function CreateBuildingPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Redirect if not authenticated
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-white">
        <header className="bg-white border-b border-neutral-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center space-x-4">
                <Link
                  href="/admin/buildings"
                  className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                >
                  <HiChevronLeft className="w-5 h-5 mr-2" />
                  <span className="font-light">Back to Buildings</span>
                </Link>
                <div className="h-6 w-px bg-neutral-300"></div>
                <Link href="/" className="text-2xl font-thin tracking-widest text-neutral-900 hover:text-neutral-700 transition-colors">
                  luyari.
                </Link>
              </div>
              <div className="text-sm text-neutral-600 font-light">
                Create Building
              </div>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-white rounded-lg border border-neutral-200 p-12 text-center"
          >
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-neutral-900 mx-auto"></div>
            <p className="text-neutral-600 font-light mt-4">Loading...</p>
          </motion.div>
        </main>
      </div>
    );
  }

  if (!session) {
    router.push('/auth/signin');
    return null;
  }

  const handleSubmit = async (formData) => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/buildings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create building');
      }

      // Redirect to the buildings list with success message
      router.push('/admin/buildings?created=true');
      
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (confirm('Are you sure you want to cancel? All unsaved changes will be lost.')) {
      router.push('/admin/buildings');
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <header className="bg-white border-b border-neutral-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin/buildings"
                className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
              >
                <HiChevronLeft className="w-5 h-5 mr-2" />
                <span className="font-light">Back to Buildings</span>
              </Link>
              <div className="h-6 w-px bg-neutral-300"></div>
              <Link href="/" className="text-2xl font-thin tracking-widest text-neutral-900 hover:text-neutral-700 transition-colors">
                luyari.
              </Link>
            </div>
            <div className="text-sm text-neutral-600 font-light">
              Create Building
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center mb-4">
            <HiOfficeBuilding className="w-8 h-8 text-neutral-600 mr-3" />
            <h1 className="chaumet-heading text-4xl text-neutral-900">Create New Building</h1>
          </div>
          <p className="text-lg text-neutral-600 font-light">Add a new building project to your portfolio</p>
          <div className="chaumet-divider mt-4 mx-auto" style={{ width: '4rem' }} />
        </motion.div>

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <HiExclamationCircle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error creating building</h3>
                <div className="mt-1 text-sm text-red-700 font-light">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        <div className="bg-white rounded-lg border border-neutral-200">
          <BuildingForm
            mode="create"
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={isLoading}
          />
        </div>
      </main>

      <footer className="bg-white border-t border-neutral-200 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <Link href="/" className="text-2xl font-thin tracking-widest text-neutral-900 hover:text-neutral-700 transition-colors">
              luyari.
            </Link>
            <p className="text-neutral-600 font-light mt-2">Building Management System</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
