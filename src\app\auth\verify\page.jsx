import { signIn } from '@/auth'
import { redirect } from 'next/navigation'
import clientPromise from '@/libs/mongodb'

export default async function VerifyPage({ searchParams }) {
  const { token, email } = searchParams
  
  if (!token || !email) {
    redirect('/auth/signin?error=InvalidToken')
  }
  
  try {
    // Verify token in database
    const client = await clientPromise
    const db = client.db()
    
    const verificationToken = await db.collection('verification_tokens').findOne({
      token,
      identifier: email,
      expires: { $gt: new Date() }
    })
    
    if (!verificationToken) {
      redirect('/auth/signin?error=ExpiredToken')
    }
    
    // Delete used token
    await db.collection('verification_tokens').deleteOne({ token })
    
    // Store user in database if not exists
    const usersCollection = db.collection('users')
    const existingUser = await usersCollection.findOne({ email })
    
    if (!existingUser) {
      await usersCollection.insertOne({
        email,
        name: email.split('@')[0],
        username: email.split('@')[0],
        phone: null,
        projects: [],
        role: email === '<EMAIL>' ? 'admin' : 'user',
        dateCreated: new Date(),
        lastLogin: new Date()
      })
    } else {
      // Update last login
      await usersCollection.updateOne(
        { email },
        { $set: { lastLogin: new Date() } }
      )
    }
    
    // Create a temporary session by redirecting to a special endpoint
    redirect(`/api/auth/magic-signin?email=${encodeURIComponent(email)}`)
    
  } catch (error) {
    console.error('Verification error:', error)
    redirect('/auth/signin?error=VerificationFailed')
  }
}
