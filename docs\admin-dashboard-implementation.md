# Admin Dashboard Implementation Guide

## Overview
This document outlines the comprehensive admin dashboard implementation with NextAuth.js v5 authentication and user management system for the Luyari site.

## Features Implemented

### 1. Authentication System (NextAuth.js v5)
- **Google OAuth Provider**: Configured with client ID and secret
- **Magic Link Email Provider**: Using Nodemailer with SMTP configuration
- **MongoDB Adapter**: Integrated with existing MongoDB database
- **Account Linking**: Automatic linking for users with same email across providers
- **Auto Admin Assignment**: <EMAIL> automatically gets admin role

### 2. User Schema & Database
- **User Fields**:
  - `username`: Display name (string)
  - `email`: Unique identifier (string)
  - `phone`: Contact number (number)
  - `projects`: Array of assigned project IDs
  - `role`: User role ('admin' or 'user')
  - `dateCreated`: Registration timestamp

### 3. Admin Dashboard Features
- **User Statistics**: Total users, admin count, regular users, recent registrations
- **User Table**: Comprehensive display with sorting and pagination
- **Search & Filter**: Search across username, email, phone, and date
- **Inline Editing**: Edit user role and phone number directly in table
- **Bulk Operations**: Select and delete multiple users
- **Individual Actions**: Edit and delete individual users
- **Project Assignment**: Assign projects to users (framework ready)

### 4. Security & Protection
- **Middleware Protection**: Admin routes protected at middleware level
- **API Route Protection**: Admin API endpoints require admin role
- **Self-Protection**: Admins cannot delete or demote themselves
- **Session Management**: Database-based sessions with role information

## File Structure

```
src/
├── auth.js                           # NextAuth.js v5 configuration
├── middleware.js                     # Route protection middleware
├── libs/
│   └── userSchema.js                 # User service and database operations
├── app/
│   ├── admin/
│   │   └── page.jsx                  # Admin dashboard main page
│   ├── auth/
│   │   ├── signin/
│   │   │   └── page.jsx              # Sign-in page
│   │   └── verify-request/
│   │       └── page.jsx              # Magic link verification page
│   └── api/
│       └── admin/
│           └── users/
│               ├── route.js          # Users CRUD operations
│               ├── stats/
│               │   └── route.js      # User statistics
│               └── [id]/
│                   └── route.js      # Individual user operations
└── components/
    └── admin/
        └── UsersManagement.jsx       # Main users management component
```

## Environment Variables Required

```env
# MongoDB
MONGODB_URI="your_mongodb_connection_string"

# NextAuth.js
AUTH_SECRET="your_auth_secret"
AUTH_URL="your_app_url"

# Google OAuth
AUTH_GOOGLE_ID="your_google_client_id"
AUTH_GOOGLE_SECRET="your_google_client_secret"

# Email Provider
EMAIL_SERVER={"host":"smtp.hostinger.com","port":465,"auth":{"user":"<EMAIL>","pass":"your_password"}}
EMAIL_FROM="<EMAIL>"
```

## API Endpoints

### User Management
- `GET /api/admin/users` - Get all users with pagination and search
- `POST /api/admin/users` - Create new user (reserved for future use)
- `DELETE /api/admin/users` - Bulk delete users
- `GET /api/admin/users/[id]` - Get specific user
- `PUT /api/admin/users/[id]` - Update user (role, phone, projects)
- `DELETE /api/admin/users/[id]` - Delete specific user
- `GET /api/admin/users/stats` - Get user statistics

### Authentication
- `GET/POST /api/auth/[...nextauth]` - NextAuth.js handlers
- `/auth/signin` - Sign-in page
- `/auth/verify-request` - Magic link verification page

## Usage Instructions

### For Admins
1. **Access Dashboard**: Navigate to `/admin` (requires admin role)
2. **View Users**: See all users with statistics and detailed table
3. **Search Users**: Use search bar to find users by name, email, or phone
4. **Edit Users**: Click edit icon to modify role or phone number
5. **Delete Users**: Use individual delete or bulk selection for multiple users
6. **Sort Data**: Click column headers to sort by different fields

### For Developers
1. **Adding New Fields**: Update `UserService` class and database schema
2. **Extending Permissions**: Modify middleware and API route checks
3. **Custom Validation**: Add validation logic in API routes
4. **UI Customization**: Modify `UsersManagement.jsx` component

## Security Considerations

1. **Role-Based Access**: Only admin users can access admin features
2. **Self-Protection**: Admins cannot delete or demote themselves
3. **Input Validation**: All user inputs are validated on both client and server
4. **Session Security**: Database-based sessions with secure configuration
5. **CSRF Protection**: NextAuth.js provides built-in CSRF protection

## Testing Recommendations

1. **Authentication Flow**: Test Google OAuth and magic link authentication
2. **Role Assignment**: Verify admin role <NAME_EMAIL>
3. **User Management**: Test all CRUD operations for users
4. **Security**: Verify middleware protection and unauthorized access prevention
5. **Edge Cases**: Test bulk operations, pagination, and search functionality

## Future Enhancements

1. **Project Management**: Implement project assignment functionality
2. **User Permissions**: Add granular permission system
3. **Audit Logs**: Track admin actions and user changes
4. **Export Features**: Add CSV/Excel export for user data
5. **Advanced Filtering**: Add date range and role-based filtering
6. **User Notifications**: Email notifications for account changes

## Troubleshooting

### Common Issues
1. **MongoDB Connection**: Verify MONGODB_URI is correct
2. **Email Delivery**: Check SMTP settings and credentials
3. **OAuth Errors**: Verify Google OAuth credentials and redirect URLs
4. **Permission Denied**: Ensure user has admin role in database
5. **Session Issues**: Clear browser cookies and restart application

### Debug Steps
1. Check browser console for JavaScript errors
2. Verify API responses in Network tab
3. Check server logs for authentication errors
4. Validate environment variables are loaded
5. Test database connectivity independently

## Dependencies Added
- `nodemailer`: For magic link email functionality
- `@auth/mongodb-adapter`: MongoDB adapter for NextAuth.js
- `react-icons`: Icons for UI components

## Git Commit Messages

```
feat: implement comprehensive admin dashboard with NextAuth.js v5

- Configure NextAuth.js v5 with Google OAuth and magic link providers
- Add MongoDB adapter with account linking functionality
- Implement auto admin role <NAME_EMAIL>
- Create user schema with username, email, phone, projects, role, dateCreated
- Build comprehensive users management interface with search, sort, pagination
- Add inline editing for user role and phone number
- Implement bulk user deletion with selection checkboxes
- Create magic link verification page with user-friendly messaging
- Add middleware protection for admin routes and API endpoints
- Implement user statistics dashboard with real-time data
- Create secure API endpoints for user CRUD operations
- Add comprehensive error handling and validation
- Include responsive design with Tailwind CSS styling
```

This implementation provides a robust, secure, and user-friendly admin dashboard that meets all the specified requirements while following Next.js best practices and maintaining code quality under 500 lines per component.
