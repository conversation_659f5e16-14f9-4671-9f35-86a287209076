// src/app/admin/buildings/[id]/edit/page.jsx
// Edit building page

"use client";

import { useState, useEffect, use } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { HiChevronLeft, HiExclamationCircle, HiRefresh } from 'react-icons/hi';
// import BuildingForm from '@/components/forms/BuildingForm';
import ExperienceWrapper from '@/components/experience/ExperienceWrapperDashboard';
import BuildingForm from '@/components/forms/BuildingForm';
import PageWrapper from '@/components/PageWrapper';

export default function EditBuildingPage({ params }) {
  const { status } = useSession();
  const router = useRouter();
  const [building, setBuilding] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState('');

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const buildingId = resolvedParams.id;

  // Fetch building data - moved before conditional returns
  useEffect(() => {
    const fetchBuilding = async () => {
      try {
        setIsLoadingData(true);
        const response = await fetch(`/api/buildings/${buildingId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Building not found');
          }
          throw new Error('Failed to fetch building data');
        }

        const data = await response.json();
        setBuilding(data.building);
        setError('');
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoadingData(false);
      }
    };

    if (buildingId) {
      fetchBuilding();
    }
  }, [buildingId]);

  // Redirect if not authenticated
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const handleSubmit = async (formData) => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/buildings/${buildingId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update building');
      }

      await response.json();

      // Redirect to the building detail page with success message
      router.push(`/admin/buildings/${buildingId}?updated=true`);

    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (confirm('Are you sure you want to cancel? All unsaved changes will be lost.')) {
      router.push(`/admin/buildings/${buildingId}`);
    }
  };

  // Loading state
  if (isLoadingData) {
    return (
      <div className="min-h-screen bg-white">
        <header className="bg-white -border-b border-neutral-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center space-x-4">
                <Link
                  href="/admin/buildings"
                  className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                >
                  <HiChevronLeft className="w-5 h-5 mr-2" />
                  <span className="font-light">Back to Buildings</span>
                </Link>
                <div className="h-6 w-px bg-neutral-300"></div>
                <Link href="/" className="text-2xl font-thin tracking-widest text-neutral-900 hover:text-neutral-700 transition-colors">
                  luyari.
                </Link>
              </div>
              <div className="text-sm text-neutral-600 font-light">
                Edit Building
              </div>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-white rounded-lg border border-neutral-200 p-12 text-center"
          >
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-neutral-900 mx-auto"></div>
            <p className="text-neutral-600 font-light mt-4">Loading building data...</p>
          </motion.div>
        </main>
      </div>
    );
  }

  // Error state
  if (error && !building) {
    return (
      <div className="min-h-screen bg-white">
        <header className="bg-white -border-b border-neutral-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center space-x-4">
                <Link
                  href="/admin/buildings"
                  className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                >
                  <HiChevronLeft className="w-5 h-5 mr-2" />
                  <span className="font-light">Back to Buildings</span>
                </Link>
                <div className="h-6 w-px bg-neutral-300"></div>
                <Link href="/" className="text-2xl font-thin tracking-widest text-neutral-900 hover:text-neutral-700 transition-colors">
                  luyari.
                </Link>
              </div>
              <div className="text-sm text-neutral-600 font-light">
                Edit Building
              </div>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 p-12">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-red-50 border border-red-200 rounded-lg px-8 text-center"
          >
            <div className="flex justify-center mb-4">
              <HiExclamationCircle className="h-12 w-12 text-red-400" />
            </div>
            <h3 className="chaumet-heading text-xl text-red-800 mb-2">Error Loading Building</h3>
            <p className="text-red-700 font-light mb-6">{error}</p>
            <div className="flex justify-center space-x-4">
              <Link
                href="/admin/buildings"
                className="chaumet-button border-red-600 text-red-600 group hover:bg-red-600"
              >
                <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center">
                  <HiChevronLeft className="w-4 h-4 mr-2" />
                  Back to Buildings
                </span>
              </Link>
              <button
                onClick={() => window.location.reload()}
                className="chaumet-button border-neutral-600 text-neutral-600 group hover:bg-neutral-600"
              >
                <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center">
                  <HiRefresh className="w-4 h-4 mr-2" />
                  Try Again
                </span>
              </button>
            </div>
          </motion.div>
        </main>
      </div>
    );
  }

  return (
    <PageWrapper>
      <div className="building-input-wrapper flex h-full w-full gap-4 px-8 justify-center">
        <div className="building-experience min-w-1/2">
          <h1 className="text-3xl font-bold text-gray-900">Building Management</h1>
          <div className='h-[calc(100%-52px)] mt-4 bg-gray-50 border-gray-100 border-2 w-full rounded-lg overflow-hidden shadow'>
            {/* <ExperienceWrapper data={building} /> */}
          </div>
        </div>
        <div className="building-input min-w-1/2 overflow-hidden">
          {/* Header */}
          <div className="mb-4">
            {/* <div className="flex items-center space-x-4">
              <Link
                href={`/admin/buildings/${buildingId}`}
                className="text-blue-600 hover:text-blue-800 flex items-center"
              >
                <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Building
              </Link>
              <span className="text-gray-300">|</span>
              <Link
                href="/admin/buildings"
                className="text-blue-600 hover:text-blue-800"
              >
                All Buildings
              </Link>
            </div> */}
            <h1 className="text-3xl font-bold text-gray-900">
              Edit Building: {building?.projectTitle}
            </h1>
            {/* <p className="text-gray-600 mt-2">Update building project information and files</p> */}
          </div>

          {/* Error Message */}
          {error && building && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <HiExclamationCircle className="h-5 w-5 text-red-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error updating building</h3>
                  <div className="mt-1 text-sm text-red-700 font-light">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Building Form */}
          {building && (
            <div className='w-full h-[calc(100%-40px)]'>
              <BuildingForm
                mode="edit"
                initialData={building}
                onSubmit={handleSubmit}
                onCancel={handleCancel}
                isLoading={isLoading}
              />
            </div>
          )}
        </div>
      </div>
    </PageWrapper>
  );
}
