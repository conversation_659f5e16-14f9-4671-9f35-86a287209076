import { NextResponse } from 'next/server'
import clientPromise from '@/libs/mongodb'
import nodemailer from 'nodemailer'
import crypto from 'crypto'

export async function POST(request) {
  try {
    const { email } = await request.json()
    
    if (!email || !email.includes('@')) {
      return NextResponse.json(
        { error: 'Valid email is required' },
        { status: 400 }
      )
    }
    
    // Generate verification token
    const token = crypto.randomBytes(32).toString('hex')
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    
    // Store token in database
    const client = await clientPromise
    const db = client.db()
    
    await db.collection('verification_tokens').insertOne({
      identifier: email,
      token,
      expires
    })
    
    // Create verification URL
    const verificationUrl = `${process.env.AUTH_URL}/auth/verify?token=${token}&email=${encodeURIComponent(email)}`
    
    // Send email
    const transporter = nodemailer.createTransporter(JSON.parse(process.env.EMAIL_SERVER))
    
    await transporter.sendMail({
      from: process.env.EMAIL_FROM,
      to: email,
      subject: 'Sign in to Luyari',
      html: `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #333; text-align: center;">Sign in to Luyari</h2>
          <p style="color: #666; font-size: 16px;">Click the button below to sign in to your account:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
              Sign In
            </a>
          </div>
          <p style="color: #666; font-size: 14px;">
            If the button doesn't work, copy and paste this link into your browser:
          </p>
          <p style="color: #2563eb; word-break: break-all; font-size: 14px;">
            ${verificationUrl}
          </p>
          <p style="color: #999; font-size: 12px; margin-top: 30px;">
            This link will expire in 24 hours. If you didn't request this email, you can safely ignore it.
          </p>
        </div>
      `
    })
    
    return NextResponse.json({ 
      success: true, 
      message: 'Magic link sent to your email' 
    })
  } catch (error) {
    console.error('Error sending magic link:', error)
    return NextResponse.json(
      { error: 'Failed to send magic link' },
      { status: 500 }
    )
  }
}
