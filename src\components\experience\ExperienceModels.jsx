'use client'
import { Environment } from '@react-three/drei'
import { useThree } from '@react-three/fiber' // Corrected: useThree is imported from @react-three/fiber
import { useEffect, useRef, useState } from 'react'
import React from 'react'
import useExperienceContext from '@/libs/contextProviders/useExperienceContext'
import ExperienceGLTFLoader from './ExperienceGLTFLoader'

export default function ExperienceModels({data}) { // data is passed from ExperienceUi component as props
  const {experienceState}=useExperienceContext() // experienceDispatch is not used here
  const {scene}=useThree() 
  const refModel=useRef(null)
  const refHideLevel=useRef(null)
  const [levelsToHideList, setLevelsToHideList]=useState([])

  /**
   * Smart Level Visibility Management
   * Implements priority-based visibility control for hideLevel objects
   */
  // Note: It's generally safer to get the group from the ref after it has been mounted,
  // but getting it from the scene by name can work if the object is guaranteed to exist.
  const hideGroup = scene.getObjectByName('hideLevel');

  const handleLevelToHide = () => {
    // Ensure the necessary data exists before proceeding
    if(!experienceState?.levelToHide || !levelsToHideList || !Array.isArray(levelsToHideList) || !hideGroup) {
      return;
    }

    let priorityVisibleList=[]
    let priorityInvisibleList=[]
    
    levelsToHideList.forEach((i)=>{
      if(i?.visible){
        priorityVisibleList.push(i?.priority || 0)
      }else{
        priorityInvisibleList.push(i?.priority || 0)
      }
    })

    if (priorityInvisibleList.length > 0 || priorityVisibleList.length > 0) {
      const minPriority = priorityInvisibleList.length > 0 ? Math.min(...priorityInvisibleList) : Infinity;
      const maxPriority = priorityVisibleList.length > 0 ? Math.max(...priorityVisibleList) : -Infinity;
      
      const targetObject = hideGroup?.getObjectByName(experienceState?.levelToHide?.name);

      if (targetObject) {
        // Find the corresponding item in our list to get its priority
        const itemToToggle = levelsToHideList.find(item => item.name === experienceState?.levelToHide?.name);
        if (itemToToggle) {
          const itemPriority = itemToToggle.priority || 0;
          if(itemPriority === minPriority){
            targetObject.visible = true;
          } else if(itemPriority === maxPriority){
            targetObject.visible = false;
          }
        }
      }
    }
  }
  
  useEffect(() => {
    const hideList = scene.getObjectByName('hideLevel')?.children;

    // Ensure hideList and data.hideLevel exist and are arrays before proceeding
    if (data?.hideLevel && Array.isArray(data.hideLevel) && hideList && Array.isArray(hideList)) {
      const hideListWithPriority = hideList.map(child => {
          const dataItem = data.hideLevel.find(i => i?.name === child.name);
          // Return a new object with priority attached, preserving the original child object
          return { ...child, priority: dataItem?.priority || 0 };
      });
      setLevelsToHideList(hideListWithPriority);
    } else {
      setLevelsToHideList(hideList || []);
    }
  }, [data, scene]); // Added dependencies to re-run if data or scene changes
  
  useEffect(() => {
    handleLevelToHide()
  }, [experienceState?.levelToHide, levelsToHideList]) // Added levelsToHideList dependency

  // console.log('ExperienceModel:',refModel.current?.position)
  
  return (
    <>
      <group 
        name="ExperienceModel"
        ref={refModel}
        // Safely parse the position prop
        position={Array.isArray(data?.position) ? data.position : data?.position?.split(',').map(i=>Number(i)) || [0, 0, 0]}
      >
        {data?.modelsFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
         <group ref={refHideLevel} name="hideLevel">
          {data?.hideLevel?.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
        {data?.supportFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        <group name="roomSnaps">
          {data?.roomSnaps?.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
      </group>
      <Environment preset="city"/>
    </>
  )
}
