'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Hi<PERSON>, <PERSON><PERSON>ser, HiMail, HiPhone, HiGlobeAlt, HiPlus, HiTrash, HiEye } from 'react-icons/hi';
import { useSession } from 'next-auth/react';
import Image from 'next/image';

export default function ProfileModal({ isOpen, onClose }) {
  const { data: session, update } = useSession();
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  
  // Profile form state
  const [profileData, setProfileData] = useState({
    username: '',
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    website: ''
  });

  // Invites state
  const [invites, setInvites] = useState([]);
  const [newInviteEmail, setNewInviteEmail] = useState('');
  const [selectedProject, setSelectedProject] = useState('');
  const [userProjects, setUserProjects] = useState([]);

  // Load user data when modal opens
  useEffect(() => {
    if (isOpen && session?.user) {
      setProfileData({
        username: session.user.username || '',
        firstName: session.user.firstName || '',
        lastName: session.user.lastName || '',
        phone: session.user.phone || '',
        email: session.user.email || '',
        website: session.user.website || ''
      });
      
      // Load user projects and invites
      loadUserData();
    }
  }, [isOpen, session]);

  const loadUserData = async () => {
    try {
      const response = await fetch('/api/user/profile');
      if (response.ok) {
        const data = await response.json();
        setUserProjects(data.projects || []);
        setInvites(data.invites || []);
        if (data.projects?.length > 0) {
          setSelectedProject(data.projects[0].id);
        }
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profileData)
      });

      if (response.ok) {
        const updatedUser = await response.json();
        await update(updatedUser);
        setMessage('Profile updated successfully!');
      } else {
        setMessage('Failed to update profile');
      }
    } catch (error) {
      setMessage('Error updating profile');
    } finally {
      setLoading(false);
    }
  };

  const handleAddInvite = async () => {
    if (!newInviteEmail || !selectedProject) return;

    setLoading(true);
    try {
      const response = await fetch('/api/user/invites', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: newInviteEmail,
          projectId: selectedProject
        })
      });

      if (response.ok) {
        const newInvite = await response.json();
        setInvites([...invites, newInvite]);
        setNewInviteEmail('');
        setMessage('Invite added successfully!');
      } else {
        setMessage('Failed to add invite');
      }
    } catch (error) {
      setMessage('Error adding invite');
    } finally {
      setLoading(false);
    }
  };

  const handleSendInvite = async (inviteId) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/user/invites/${inviteId}/send`, {
        method: 'POST'
      });

      if (response.ok) {
        setInvites(invites.map(invite => 
          invite.id === inviteId 
            ? { ...invite, status: 'sent', sentAt: new Date().toISOString() }
            : invite
        ));
        setMessage('Invite sent successfully!');
      } else {
        setMessage('Failed to send invite');
      }
    } catch (error) {
      setMessage('Error sending invite');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteInvite = async (inviteId) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/user/invites/${inviteId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setInvites(invites.filter(invite => invite.id !== inviteId));
        setMessage('Invite deleted successfully!');
      } else {
        setMessage('Failed to delete invite');
      }
    } catch (error) {
      setMessage('Error deleting invite');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="profile-modal-overlay"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="profile-modal"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-neutral-200">
            <h2 className="chaumet-heading text-2xl text-neutral-900">Account Settings</h2>
            <button
              onClick={onClose}
              className="text-neutral-400 hover:text-neutral-600 transition-colors"
            >
              <HiX className="w-6 h-6" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-neutral-200">
            <button
              onClick={() => setActiveTab('profile')}
              className={`px-6 py-3 text-sm font-light tracking-wider uppercase transition-colors ${
                activeTab === 'profile'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-neutral-600 hover:text-neutral-900'
              }`}
            >
              Profile
            </button>
            <button
              onClick={() => setActiveTab('invites')}
              className={`px-6 py-3 text-sm font-light tracking-wider uppercase transition-colors ${
                activeTab === 'invites'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-neutral-600 hover:text-neutral-900'
              }`}
            >
              Invites
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {message && (
              <div className={`mb-4 p-3 rounded-lg text-sm ${
                message.includes('successfully') 
                  ? 'bg-green-50 text-green-700 border border-green-200'
                  : 'bg-red-50 text-red-700 border border-red-200'
              }`}>
                {message}
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="flex gap-8">
                {/* Profile Picture */}
                <div className="flex-shrink-0">
                  <div className="w-32 h-32 rounded-full overflow-hidden bg-neutral-200 flex items-center justify-center">
                    {session?.user?.image ? (
                      <Image
                        src={session.user.image}
                        alt="Profile"
                        width={128}
                        height={128}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <HiUser className="w-16 h-16 text-neutral-400" />
                    )}
                  </div>
                  <button className="mt-3 text-sm text-blue-600 hover:text-blue-700 font-light tracking-wider uppercase">
                    Change
                  </button>
                </div>

                {/* Profile Form */}
                <div className="flex-1">
                  <form onSubmit={handleProfileUpdate} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-light text-neutral-600 mb-2 tracking-wider uppercase">
                          First Name
                        </label>
                        <input
                          type="text"
                          value={profileData.firstName}
                          onChange={(e) => setProfileData({...profileData, firstName: e.target.value})}
                          className="chaumet-input"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-light text-neutral-600 mb-2 tracking-wider uppercase">
                          Last Name
                        </label>
                        <input
                          type="text"
                          value={profileData.lastName}
                          onChange={(e) => setProfileData({...profileData, lastName: e.target.value})}
                          className="chaumet-input"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-light text-neutral-600 mb-2 tracking-wider uppercase">
                        Username
                      </label>
                      <input
                        type="text"
                        value={profileData.username}
                        onChange={(e) => setProfileData({...profileData, username: e.target.value})}
                        className="chaumet-input"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-light text-neutral-600 mb-2 tracking-wider uppercase">
                        Email
                      </label>
                      <input
                        type="email"
                        value={profileData.email}
                        onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                        className="chaumet-input"
                        disabled
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-light text-neutral-600 mb-2 tracking-wider uppercase">
                        Phone
                      </label>
                      <input
                        type="tel"
                        value={profileData.phone}
                        onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                        className="chaumet-input"
                        placeholder="****** 56 66 777"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-light text-neutral-600 mb-2 tracking-wider uppercase">
                        Website
                      </label>
                      <input
                        type="url"
                        value={profileData.website}
                        onChange={(e) => setProfileData({...profileData, website: e.target.value})}
                        className="chaumet-input"
                        placeholder="https://yourwebsite.com"
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={loading}
                      className="chaumet-button border-blue-600 text-blue-600 group mt-6"
                    >
                      <span className="relative z-10 group-hover:text-white transition-colors duration-500">
                        {loading ? 'Saving...' : 'Save'}
                      </span>
                    </button>
                  </form>
                </div>
              </div>
            )}

            {activeTab === 'invites' && (
              <div className="space-y-6">
                {/* Add New Invite */}
                <div className="bg-neutral-50 p-6 rounded-lg">
                  <h3 className="chaumet-heading text-lg mb-4 text-neutral-900">Add New Invite</h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-light text-neutral-600 mb-2 tracking-wider uppercase">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={newInviteEmail}
                        onChange={(e) => setNewInviteEmail(e.target.value)}
                        className="chaumet-input"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-light text-neutral-600 mb-2 tracking-wider uppercase">
                        Project to Share
                      </label>
                      <select
                        value={selectedProject}
                        onChange={(e) => setSelectedProject(e.target.value)}
                        className="chaumet-select"
                      >
                        <option value="">Select a project</option>
                        {userProjects.map((project) => (
                          <option key={project.id} value={project.id}>
                            {project.title}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="flex items-end">
                      <button
                        onClick={handleAddInvite}
                        disabled={!newInviteEmail || !selectedProject || loading}
                        className="chaumet-button border-blue-600 text-blue-600 group w-full"
                      >
                        <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center">
                          <HiPlus className="w-4 h-4 mr-2" />
                          Add Invite
                        </span>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Invites List */}
                <div>
                  <h3 className="chaumet-heading text-lg mb-4 text-neutral-900">Pending Invites</h3>

                  {invites.length === 0 ? (
                    <div className="text-center py-8 text-neutral-500">
                      <HiMail className="w-12 h-12 mx-auto mb-4 text-neutral-300" />
                      <p className="font-light">No invites yet. Add some colleagues to share your projects!</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {invites.map((invite) => (
                        <div key={invite.id} className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <HiMail className="w-5 h-5 text-neutral-400" />
                              <div>
                                <p className="font-light text-neutral-900">{invite.email}</p>
                                <p className="text-sm text-neutral-500">
                                  Project: {userProjects.find(p => p.id === invite.projectId)?.title || 'Unknown'}
                                </p>
                                {invite.sentAt && (
                                  <p className="text-xs text-neutral-400">
                                    Sent: {new Date(invite.sentAt).toLocaleDateString()}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              invite.status === 'sent'
                                ? 'bg-green-100 text-green-700'
                                : invite.status === 'viewed'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-yellow-100 text-yellow-700'
                            }`}>
                              {invite.status || 'pending'}
                            </span>

                            {invite.status !== 'sent' && (
                              <button
                                onClick={() => handleSendInvite(invite.id)}
                                disabled={loading}
                                className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                                title="Send invite"
                              >
                                <HiEye className="w-4 h-4" />
                              </button>
                            )}

                            <button
                              onClick={() => handleDeleteInvite(invite.id)}
                              disabled={loading}
                              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                              title="Delete invite"
                            >
                              <HiTrash className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
