'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense } from 'react'
// import ExperienceControls from './ExperienceControls'
// import ExperienceModels from './ExperienceModels'
import LoadingSpinner from '../LoadingSpinner'
import dynamic from 'next/dynamic';

const ExperienceControls=dynamic(() => import('./ExperienceControls'),{ssr:false})
const ExperienceModels=dynamic(() => import('./ExperienceModels'),{ssr:false})

export default function ExperienceWorld({data}) {
  return (
    <Canvas>
        <Suspense fallback={<LoadingSpinner/>}>
          <ExperienceControls data={data}/>
          <ExperienceModels data={data}/>
        </Suspense>
    </Canvas>
  )
}
