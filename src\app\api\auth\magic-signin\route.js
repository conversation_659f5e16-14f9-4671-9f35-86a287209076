import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import clientPromise from '@/libs/mongodb'

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    
    if (!email) {
      return NextResponse.redirect(new URL('/auth/signin?error=InvalidEmail', request.url))
    }
    
    // Get user from database
    const client = await clientPromise
    const db = client.db()
    const user = await db.collection('users').findOne({ email })
    
    if (!user) {
      return NextResponse.redirect(new URL('/auth/signin?error=UserNotFound', request.url))
    }
    
    // Create JWT token
    const token = jwt.sign(
      {
        sub: user._id.toString(),
        email: user.email,
        name: user.name,
        role: user.role,
        phone: user.phone,
        projects: user.projects || [],
        dateCreated: user.dateCreated,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days
      },
      process.env.AUTH_SECRET
    )
    
    // Set session cookie
    const cookieStore = cookies()
    cookieStore.set('next-auth.session-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30 * 24 * 60 * 60 // 30 days
    })
    
    // Redirect to home or callback URL
    const callbackUrl = searchParams.get('callbackUrl') || '/'
    return NextResponse.redirect(new URL(callbackUrl, request.url))
    
  } catch (error) {
    console.error('Magic signin error:', error)
    return NextResponse.redirect(new URL('/auth/signin?error=SigninFailed', request.url))
  }
}
