import { NextResponse } from 'next/server'
import clientPromise from '@/libs/mongodb'

export async function POST(request) {
  try {
    const userData = await request.json()
    
    const client = await clientPromise
    const db = client.db()
    const collection = db.collection('users')
    
    // Check if user already exists
    const existingUser = await collection.findOne({ email: userData.email })
    
    if (existingUser) {
      // Update existing user with new login info
      await collection.updateOne(
        { email: userData.email },
        {
          $set: {
            name: userData.name,
            image: userData.image,
            role: userData.role,
            lastLogin: new Date()
          }
        }
      )
    } else {
      // Create new user
      await collection.insertOne({
        email: userData.email,
        name: userData.name,
        username: userData.name || userData.email?.split('@')[0] || 'User',
        image: userData.image,
        phone: userData.phone || null,
        projects: userData.projects || [],
        role: userData.role,
        dateCreated: new Date(userData.dateCreated),
        lastLogin: new Date()
      })
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error storing user:', error)
    return NextResponse.json(
      { error: 'Failed to store user' },
      { status: 500 }
    )
  }
}
