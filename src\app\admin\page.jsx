import PageWrapper from '@/components/PageWrapper'
import React from 'react'

export default function page() {
  const projects =[0,1,2,3,4,5,6,7,8,9,10]
  return (
    <PageWrapper>
      <div className='flex w-full flex-col gap-5'>
        <h1 className='text-3xl font-bold'>Projects</h1>
        <p className='text-xl font-bold'>Projects</p>
        <div className='flex w-full h-full flex-wrap overflow-y-auto scrollbar-hide'>
          {projects.map(project => 
            <div
              key={project}
              className='w-1/3 h-2/3 p-2 flex flex-col justify-center items-center'
            >
              <div className='w-full h-full bg-gray-100 rounded-lg p-5 flex flex-col justify-center items-center'> 
                <h2 className='text-2xl font-bold'>Project 1</h2>
                <p className='text-lg font-light'>Project 1 description</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </PageWrapper>
  )
}
