// Force Node.js runtime for email functionality
export const runtime = 'nodejs'

import { NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

export async function POST(request) {
  try {
    const { to, subject, html } = await request.json()
    
    if (!to || !subject || !html) {
      return NextResponse.json(
        { error: 'Missing required fields: to, subject, html' },
        { status: 400 }
      )
    }
    
    // Create transporter using environment variables
    const transporter = nodemailer.createTransporter({
      host: "smtp.hostinger.com",
      port: 465,
      secure: true,
      auth: {
        user: "<EMAIL>",
        pass: process.env.EMAIL_PASSWORD,
      },
    })
    
    // Send email
    await transporter.sendMail({
      from: "<EMAIL>",
      to,
      subject,
      html,
    })
    
    return NextResponse.json({ 
      success: true, 
      message: 'Email sent successfully' 
    })
  } catch (error) {
    console.error('Error sending email:', error)
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    )
  }
}
