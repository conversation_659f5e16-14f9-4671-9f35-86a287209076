'use client';

import React, { useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { FaGoogle } from 'react-icons/fa'; // FaFacebook removed - Facebook login disabled
import { MdLogout, MdLogin } from 'react-icons/md';
import { signIn, signOut } from 'next-auth/react';
import { useSession } from 'next-auth/react';

export default function AuthPopup({ isOpen, onClose }) {
  const popupRef = useRef(null);
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated' && session?.user;

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Handle social sign-in
  const handleSocialSignIn = async (provider) => {
    try {
      console.log(`Attempting to sign in with ${provider}...`);

      const result = await signIn(provider, {
        callbackUrl: '/dashboard',
        redirect: false,
      });

      console.log(`Sign in result:`, result);

      if (result?.error) {
        console.error(`${provider} sign in error:`, result.error);

        // Handle OAuth account linking error
        if (result.error === 'OAuthAccountNotLinked') {
          // Extract email from error or use current session email
          const email = session?.user?.email || '';
          window.location.href = `/auth/signin?error=OAuthAccountNotLinked&email=${encodeURIComponent(email)}`;
          return;
        }

        alert(`Error signing in with ${provider}: ${result.error}`);
      } else if (result?.url) {
        // Successful sign-in, manually redirect
        window.location.href = result.url;
      }

      onClose();
    } catch (error) {
      console.error(`${provider} sign in error:`, error);
      alert(`Error signing in with ${provider}: ${error.message || 'Unknown error'}`);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      console.log('Attempting to sign out...');

      const result = await signOut({
        callbackUrl: '/',
        redirect: false
      });

      console.log('Sign out result:', result);

      if (result?.url) {
        // Successful sign-out, manually redirect
        window.location.href = result.url;
      }

      onClose();
    } catch (error) {
      console.error('Sign out error:', error);
      alert(`Error signing out: ${error.message || 'Unknown error'}`);
    }
  };

  // If popup is not open, don't render anything
  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-start justify-end"
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.3)' }}
    >
      <motion.div
        ref={popupRef}
        initial={{ x: 300, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: 300, opacity: 0 }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        className="bg-white shadow-lg w-80 mt-16 mr-4 overflow-hidden"
      >
        {isAuthenticated ? (
          // User is logged in - show user details
          <div className="p-6">
            <div className="flex flex-col items-center mb-6">
              <div className="relative w-20 h-20 mb-4 overflow-hidden rounded-full border-2 border-[#D4AF37]">
                {session.user.image ? (
                  <Image
                    src={session.user.image}
                    alt={session.user.name || 'User'}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center text-gray-500 text-2xl font-light">
                    {(session.user.name?.charAt(0) || session.user.email?.charAt(0) || '?').toUpperCase()}
                  </div>
                )}
              </div>
              <h3 className="text-xl font-light text-gray-800">{session.user.name || 'User'}</h3>
              <p className="text-sm text-gray-500 font-extralight">{session.user.email}</p>
              {session.user.role && (
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-2 ${
                  session.user.role === 'admin'
                    ? 'bg-purple-100 text-purple-800'
                    : session.user.role === 'client'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-green-100 text-green-800'
                }`}>
                  {session.user.role}
                </span>
              )}
              {session.user.username && (
                <p className="text-sm text-gray-400 font-extralight mt-1">@{session.user.username}</p>
              )}
            </div>

            <div className="space-y-3">
              {session.user.role === 'admin' && (
                <Link
                  href="/admin/users"
                  className="block w-full py-2 px-4 text-center text-sm font-light text-gray-700 bg-gray-50 hover:bg-gray-100 transition-colors"
                  onClick={onClose}
                >
                  Admin Dashboard
                </Link>
              )}
              <Link
                href="/dashboard"
                className="block w-full py-2 px-4 text-center text-sm font-light text-gray-700 bg-gray-50 hover:bg-gray-100 transition-colors"
                onClick={onClose}
              >
                Dashboard
              </Link>
              <Link
                href="/profile"
                className="block w-full py-2 px-4 text-center text-sm font-light text-gray-700 bg-gray-50 hover:bg-gray-100 transition-colors"
                onClick={onClose}
              >
                Profile Settings
              </Link>
              <button
                onClick={handleSignOut}
                className="w-full flex items-center justify-center py-2 px-4 bg-[#D4AF37] text-white font-light tracking-wider hover:bg-[#B8860B] transition-colors"
              >
                <MdLogout className="mr-2" />
                Sign Out
              </button>
            </div>
          </div>
        ) : (
          // User is not logged in - show login/register options
          <div className="p-6">
            <h3 className="text-xl font-light text-center text-gray-800 mb-4">Account</h3>
            <div className="w-12 h-0.5 bg-[#D4AF37] mx-auto mb-6"></div>

            <div className="space-y-3 mb-6">
              <button
                onClick={() => handleSocialSignIn('google')}
                className="w-full flex items-center justify-center py-2 px-4 border border-gray-300 rounded-none shadow-sm text-sm font-light text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                <FaGoogle className="h-4 w-4 text-[#4285F4] mr-2" />
                Continue with Google
              </button>

              <Link
                href="/auth/signin"
                className="w-full flex items-center justify-center py-2 px-4 bg-[#D4AF37] text-white font-light tracking-wider hover:bg-[#B8860B] transition-colors"
                onClick={onClose}
              >
                <MdLogin className="mr-2" />
                Sign in with Magic Link
              </Link>
            </div>
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
