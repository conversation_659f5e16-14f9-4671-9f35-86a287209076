import { signIn } from '@/auth'
import { FiMail, FiUser } from 'react-icons/fi'
import { FcGoogle } from 'react-icons/fc'
import MagicLinkForm from '@/components/auth/MagicLinkForm'

export default function SignInPage({ searchParams }) {
  const callbackUrl = searchParams?.callbackUrl || '/'

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <FiUser className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome Back
          </h1>
          <p className="text-gray-600">
            Sign in to your account to continue
          </p>
        </div>

        <div className="space-y-4">
          {/* Google Sign In */}
          <form
            action={async () => {
              'use server'
              await signIn('google', { redirectTo: callbackUrl })
            }}
          >
            <button
              type="submit"
              className="w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors font-medium"
            >
              <FcGoogle className="w-5 h-5" />
              Continue with Google
            </button>
          </form>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or</span>
            </div>
          </div>

          {/* Email Magic Link */}
          <MagicLinkForm callbackUrl={callbackUrl} />

        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            By signing in, you agree to our terms of service and privacy policy.
          </p>
        </div>

        {searchParams?.error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-700">
              {searchParams.error === 'OAuthSignin' && 'Error occurred during OAuth sign in.'}
              {searchParams.error === 'OAuthCallback' && 'Error occurred during OAuth callback.'}
              {searchParams.error === 'OAuthCreateAccount' && 'Could not create OAuth account.'}
              {searchParams.error === 'EmailCreateAccount' && 'Could not create email account.'}
              {searchParams.error === 'Callback' && 'Error occurred during callback.'}
              {searchParams.error === 'OAuthAccountNotLinked' && 'OAuth account not linked. Try signing in with a different method.'}
              {searchParams.error === 'EmailSignin' && 'Check your email for the sign in link.'}
              {searchParams.error === 'CredentialsSignin' && 'Sign in failed. Check your credentials.'}
              {searchParams.error === 'SessionRequired' && 'Please sign in to access this page.'}
              {!['OAuthSignin', 'OAuthCallback', 'OAuthCreateAccount', 'EmailCreateAccount', 'Callback', 'OAuthAccountNotLinked', 'EmailSignin', 'CredentialsSignin', 'SessionRequired'].includes(searchParams.error) && 'An error occurred during sign in.'}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
