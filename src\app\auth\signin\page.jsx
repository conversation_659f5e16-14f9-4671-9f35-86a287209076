import { signIn } from '@/auth'
import { FiMail, FiUser } from 'react-icons/fi'
import { FcGoogle } from 'react-icons/fc'

export default async function SignInPage({ searchParams }) {
  const params = await searchParams
  const callbackUrl = params?.callbackUrl || '/'

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <FiUser className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome Back
          </h1>
          <p className="text-gray-600">
            Sign in to your account to continue
          </p>
        </div>

        <div className="space-y-4">
          {/* Google Sign In */}
          <form
            action={async () => {
              'use server'
              await signIn('google', { redirectTo: callbackUrl })
            }}
          >
            <button
              type="submit"
              className="w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors font-medium"
            >
              <FcGoogle className="w-5 h-5" />
              Continue with Google
            </button>
          </form>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or</span>
            </div>
          </div>

          {/* Email Magic Link */}
          <form
            action={async (formData) => {
              'use server'
              const email = formData.get('email')
              await signIn('nodemailer', {
                email,
                redirectTo: callbackUrl
              })
            }}
            className="space-y-4"
          >
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email address
              </label>
              <div className="relative">
                <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  placeholder="Enter your email"
                  className="pl-10 pr-4 py-3 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Send Magic Link
            </button>
          </form>

        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            By signing in, you agree to our terms of service and privacy policy.
          </p>
        </div>

        {params?.error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-700">
              {params.error === 'OAuthSignin' && 'Error occurred during OAuth sign in.'}
              {params.error === 'OAuthCallback' && 'Error occurred during OAuth callback.'}
              {params.error === 'OAuthCreateAccount' && 'Could not create OAuth account.'}
              {params.error === 'EmailCreateAccount' && 'Could not create email account.'}
              {params.error === 'Callback' && 'Error occurred during callback.'}
              {params.error === 'OAuthAccountNotLinked' && 'OAuth account not linked. Try signing in with a different method.'}
              {params.error === 'EmailSignin' && 'Check your email for the sign in link.'}
              {params.error === 'CredentialsSignin' && 'Sign in failed. Check your credentials.'}
              {params.error === 'SessionRequired' && 'Please sign in to access this page.'}
              {!['OAuthSignin', 'OAuthCallback', 'OAuthCreateAccount', 'EmailCreateAccount', 'Callback', 'OAuthAccountNotLinked', 'EmailSignin', 'CredentialsSignin', 'SessionRequired'].includes(params.error) && 'An error occurred during sign in.'}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
